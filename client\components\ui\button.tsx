import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-base font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 touch-manipulation",
  {
    variants: {
      variant: {
        default: "bg-ethos-purple text-white hover:bg-ethos-purple-light transition-colors duration-300",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-ethos-purple text-ethos-purple bg-background hover:bg-ethos-purple hover:text-white transition-colors duration-300",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-ethos-purple underline-offset-4 hover:underline",
        cta: "bg-ethos-purple hover:bg-ethos-purple-light text-white rounded-full font-medium transition-colors duration-300",
        gradient: "bg-gradient-to-r from-purple-400 to-pink-400 text-white hover:from-purple-500 hover:to-pink-500 transition-all duration-300 shadow-lg hover:shadow-purple-300/50",
        "mobile-primary": "bg-ethos-purple text-white hover:bg-ethos-purple-light min-h-[48px] min-w-[48px] touch-manipulation transition-colors duration-300",
        "mobile-secondary": "border border-ethos-purple text-ethos-purple bg-background hover:bg-ethos-purple hover:text-white min-h-[48px] min-w-[48px] touch-manipulation transition-colors duration-300",
        "mobile-cta": "bg-ethos-purple hover:bg-ethos-purple-light text-white rounded-full font-medium transition-colors duration-300 min-h-[48px] min-w-[48px] touch-manipulation",
      },
      size: {
        default: "h-11 px-4 py-2 text-base sm:text-lg",
        sm: "h-11 rounded-md px-3 text-sm sm:text-base",
        lg: "h-12 rounded-md px-8 text-base sm:text-lg md:text-xl",
        icon: "h-11 w-11 text-base",
        cta: "h-12 sm:h-14 md:h-16 lg:h-18 px-6 sm:px-8 md:px-10 lg:px-12 py-3 lg:py-4 text-base sm:text-lg md:text-xl lg:text-2xl",
        "mobile-sm": "h-12 px-4 py-2 text-base min-w-[48px]",
        "mobile-default": "h-12 px-6 py-3 text-base min-w-[48px]",
        "mobile-lg": "h-14 px-8 py-4 text-lg min-w-[56px]",
        "mobile-icon": "h-12 w-12 text-base min-w-[48px] min-h-[48px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
